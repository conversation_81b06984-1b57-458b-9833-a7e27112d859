


# user_name = input("Enter your name: ")
# print("Hello,", user_name)


# for i in range(1,8):
#     print(i)

#     skills = ["Python", "Machine Learning", "NLP"]

#     for skill in skills:
#         print( "I can use", skill)


skills = ["Python", "AI", "ML", "Data Science"]

filtered = []
# for skill in skills:
#     if "a" in skill:
#         filtered.append(skill)

# print(filtered)

# for skill in skills:
#     if len(skill.split())>0:
#         filtered.append(skill)
#         break
#         print(filtered)

# Reading text file (existing code)
with open("sample.txt", "r") as file:
    content = file.read()

print("Content from sample.txt:")
print(content)

print("\n" + "="*50)
print("PDF READING PRACTICE")
print("="*50)

# PDF Reading Function
def read_pdf(file_path):
    """
    Function to read PDF files using pdfplumber library

    Args:
        file_path (str): Path to the PDF file

    Returns:
        str: Extracted text from the PDF
    """
    try:
        import pdfplumber

        text = ""
        with pdfplumber.open(file_path) as pdf:
            print(f"PDF has {len(pdf.pages)} pages")

            for page_num, page in enumerate(pdf.pages, 1):
                print(f"Reading page {page_num}...")
                page_text = page.extract_text()
                if page_text:
                    text += f"\n--- Page {page_num} ---\n"
                    text += page_text + "\n"
                else:
                    text += f"\n--- Page {page_num} (No text found) ---\n"

        return text

    except ImportError:
        return "Error: pdfplumber library not installed. Please install it using: pip install pdfplumber"
    except FileNotFoundError:
        return f"Error: File '{file_path}' not found."
    except Exception as e:
        return f"Error reading PDF: {str(e)}"

# Alternative: Create a simple PDF from text (requires reportlab)
def create_simple_pdf(text_content, output_filename):
    """
    Create a simple PDF from text content

    Args:
        text_content (str): Text to put in the PDF
        output_filename (str): Name of the output PDF file
    """
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter

        c = canvas.Canvas(output_filename, pagesize=letter)
        width, height = letter

        # Split text into lines
        lines = text_content.split('\n')
        y_position = height - 50  # Start from top

        for line in lines:
            if y_position < 50:  # Start new page if needed
                c.showPage()
                y_position = height - 50

            c.drawString(50, y_position, line[:80])  # Limit line length
            y_position -= 20

        c.save()
        print(f"PDF created successfully: {output_filename}")

    except ImportError:
        print("Error: reportlab library not installed. Install with: pip install reportlab")
    except Exception as e:
        print(f"Error creating PDF: {str(e)}")

# SOLUTION FOR MISSING PIP/LIBRARIES
print("\n" + "="*50)
print("PDF PRACTICE - STEP BY STEP SOLUTION")
print("="*50)

def check_library_installation():
    """Check which PDF libraries are available"""
    libraries = {
        'pdfplumber': False,
        'PyPDF2': False,
        'reportlab': False
    }

    for lib in libraries:
        try:
            __import__(lib)
            libraries[lib] = True
            print(f"✅ {lib} is installed")
        except ImportError:
            print(f"❌ {lib} is NOT installed")

    return libraries

def install_instructions():
    """Provide installation instructions for different scenarios"""
    print("\n" + "="*50)
    print("INSTALLATION INSTRUCTIONS")
    print("="*50)

    print("🔧 METHOD 1: Try these commands one by one:")
    print("   python -m pip install pdfplumber")
    print("   py -m pip install pdfplumber")
    print("   python3 -m pip install pdfplumber")

    print("\n🔧 METHOD 2: If pip is not working:")
    print("   1. Download Python from https://python.org")
    print("   2. During installation, check 'Add Python to PATH'")
    print("   3. Restart your computer")
    print("   4. Try: pip install pdfplumber")

    print("\n🔧 METHOD 3: Alternative approach:")
    print("   1. Download the library manually")
    print("   2. Or use conda: conda install -c conda-forge pdfplumber")

# Test current setup
print("Checking your current library installation...")
available_libs = check_library_installation()

# First, let's try to read our sample text file
try:
    with open("sample_resume.txt", "r") as file:
        resume_text = file.read()
    print("\n✅ Sample resume text loaded successfully!")
    print("First 200 characters:")
    print(resume_text[:200] + "...")

    # If no PDF libraries are available, show instructions
    if not any(available_libs.values()):
        print("\n⚠️  No PDF libraries found!")
        install_instructions()
        print("\n📝 For now, you can practice with text files.")
        print("Once you install pdfplumber, uncomment the PDF code below.")
    else:
        print("\n🎉 Great! You have PDF libraries installed.")
        print("You can now test the PDF functionality!")

except FileNotFoundError:
    print("❌ sample_resume.txt not found. Please create it first.")

# Example usage (uncomment when you have the libraries installed)
print("\n" + "="*30)
print("READY TO TEST PDF READING")
print("="*30)
print("Uncomment the lines below when pdfplumber is installed:")
print()
print("# Step 1: Create a PDF from text")
print("# create_simple_pdf(resume_text, 'sample_resume.pdf')")
print()
print("# Step 2: Read the PDF back")
print("# pdf_content = read_pdf('sample_resume.pdf')")
print("# print('\\nExtracted from PDF:')")
print("# print(pdf_content)")


