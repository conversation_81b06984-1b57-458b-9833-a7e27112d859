


# user_name = input("Enter your name: ")
# print("Hello,", user_name)


# for i in range(1,8):
#     print(i)

#     skills = ["Python", "Machine Learning", "NLP"]

#     for skill in skills:
#         print( "I can use", skill)


skills = ["Python", "AI", "ML", "Data Science"]

filtered = []
# for skill in skills:
#     if "a" in skill:
#         filtered.append(skill)

# print(filtered)

# for skill in skills:
#     if len(skill.split())>0:
#         filtered.append(skill)
#         break
#         print(filtered)

# Reading text file (existing code)
with open("sample.txt", "r") as file:
    content = file.read()

print("Content from sample.txt:")
print(content)

print("\n" + "="*50)
print("PDF READING PRACTICE")
print("="*50)

# PDF Reading Function
def read_pdf(file_path):
    """
    Function to read PDF files using pdfplumber library

    Args:
        file_path (str): Path to the PDF file

    Returns:
        str: Extracted text from the PDF
    """
    try:
        import pdfplumber

        text = ""
        with pdfplumber.open(file_path) as pdf:
            print(f"PDF has {len(pdf.pages)} pages")

            for page_num, page in enumerate(pdf.pages, 1):
                print(f"Reading page {page_num}...")
                page_text = page.extract_text()
                if page_text:
                    text += f"\n--- Page {page_num} ---\n"
                    text += page_text + "\n"
                else:
                    text += f"\n--- Page {page_num} (No text found) ---\n"

        return text

    except ImportError:
        return "Error: pdfplumber library not installed. Please install it using: pip install pdfplumber"
    except FileNotFoundError:
        return f"Error: File '{file_path}' not found."
    except Exception as e:
        return f"Error reading PDF: {str(e)}"

# Alternative: Create a simple PDF from text (requires reportlab)
def create_simple_pdf(text_content, output_filename):
    """
    Create a simple PDF from text content

    Args:
        text_content (str): Text to put in the PDF
        output_filename (str): Name of the output PDF file
    """
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter

        c = canvas.Canvas(output_filename, pagesize=letter)
        width, height = letter

        # Split text into lines
        lines = text_content.split('\n')
        y_position = height - 50  # Start from top

        for line in lines:
            if y_position < 50:  # Start new page if needed
                c.showPage()
                y_position = height - 50

            c.drawString(50, y_position, line[:80])  # Limit line length
            y_position -= 20

        c.save()
        print(f"PDF created successfully: {output_filename}")

    except ImportError:
        print("Error: reportlab library not installed. Install with: pip install reportlab")
    except Exception as e:
        print(f"Error creating PDF: {str(e)}")

# Test the PDF functionality
print("\n" + "="*50)
print("TESTING PDF FUNCTIONALITY")
print("="*50)

# First, let's try to read our sample text file
try:
    with open("sample_resume.txt", "r") as file:
        resume_text = file.read()
    print("Sample resume text loaded successfully!")
    print("First 200 characters:")
    print(resume_text[:200] + "...")
except FileNotFoundError:
    print("sample_resume.txt not found. Please create it first.")

# Example usage (uncomment when you have the libraries installed)
#
# Step 1: Create a PDF from text
# create_simple_pdf(resume_text, "sample_resume.pdf")
#
# Step 2: Read the PDF back
# pdf_content = read_pdf("sample_resume.pdf")
# print("\nExtracted from PDF:")
# print(pdf_content)


