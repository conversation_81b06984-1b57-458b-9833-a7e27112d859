# PDF Reading Practice Guide

## 🎯 What You'll Learn
- How to install Python libraries for PDF processing
- Read PDF files and extract text content
- Handle different types of PDF files (text-based vs image-based)
- Create simple PDFs from text

## 📋 Prerequisites Setup

### Step 1: Verify Python Installation
Open Command Prompt (cmd) or PowerShell and run:
```bash
python --version
```
If Python is not found, install it from [python.org](https://python.org)

### Step 2: Install Required Libraries
```bash
# For reading PDFs
pip install pdfplumber

# Alternative PDF library
pip install PyPDF2

# For creating PDFs (optional)
pip install reportlab
```

## 🔧 Your Current Setup

I've already added PDF reading functionality to your `hello.py` file with:

1. **`read_pdf(file_path)`** - Main function to read PDF files
2. **`create_simple_pdf(text, filename)`** - Function to create PDFs from text
3. **Error handling** - Graceful handling of missing libraries
4. **Sample resume text** - Test data in `sample_resume.txt`

## 🚀 How to Test

### Method 1: Using an Existing PDF
1. Place any PDF file in your project folder
2. Update the file path in `hello.py`:
   ```python
   pdf_content = read_pdf("your_pdf_file.pdf")
   print(pdf_content)
   ```

### Method 2: Create and Read a PDF
1. First install the libraries:
   ```bash
   pip install pdfplumber reportlab
   ```

2. Uncomment the test code in `hello.py`:
   ```python
   # Uncomment these lines:
   create_simple_pdf(resume_text, "sample_resume.pdf")
   pdf_content = read_pdf("sample_resume.pdf")
   print(pdf_content)
   ```

3. Run the script:
   ```bash
   python hello.py
   ```

## 📖 Understanding the Code

### PDF Reading Function
```python
def read_pdf(file_path):
    import pdfplumber
    
    text = ""
    with pdfplumber.open(file_path) as pdf:
        for page in pdf.pages:
            text += page.extract_text()
    return text
```

### Key Features:
- **Page-by-page processing**: Handles multi-page PDFs
- **Error handling**: Catches file not found and import errors
- **Text extraction**: Gets readable text from PDF content

## 🎯 Practice Exercises

1. **Basic Reading**: Read a simple PDF and print its content
2. **Page Analysis**: Count pages and analyze each page separately
3. **Text Processing**: Extract specific information (emails, phone numbers)
4. **Resume Parsing**: Extract skills, experience, education sections

## 🔍 Troubleshooting

### Common Issues:
1. **"Module not found"**: Install the required library with pip
2. **"No text extracted"**: PDF might be image-based (needs OCR)
3. **"Permission denied"**: PDF might be password-protected

### Solutions:
- Use `pip list` to check installed packages
- Try different PDF libraries (PyPDF2, pdfminer)
- For image PDFs, use OCR libraries like `pytesseract`

## 📚 Next Steps

After mastering basic PDF reading:
1. Learn about OCR for image-based PDFs
2. Extract tables and structured data
3. Build a resume parser application
4. Process multiple PDFs in batch

## 🎉 Ready to Start!

Your `hello.py` file is ready with all the necessary functions. Just install the libraries and start experimenting!
