# 🐍 Windows Python & pip Setup Guide

## 🚨 Current Issue
You're getting: `pip : The term 'pip' is not recognized`

This means pip is not in your system PATH or Python wasn't installed properly.

## 🔧 Quick Solutions (Try in Order)

### Solution 1: Try Different Python Commands
Open PowerShell/Command Prompt and try these one by one:

```powershell
# Try these commands:
python --version
py --version
python3 --version

# If any work, then try:
python -m pip install pdfplumber
py -m pip install pdfplumber
python3 -m pip install pdfplumber
```

### Solution 2: Proper Python Installation
1. **Download Python**: Go to https://python.org/downloads
2. **Important**: During installation, CHECK "Add Python to PATH"
3. **Restart** your computer after installation
4. **Test**: Open new PowerShell and try `python --version`

### Solution 3: Manual pip Installation
If Python is installed but pip is missing:

```powershell
# Download get-pip.py
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py

# Install pip
python get-pip.py
```

### Solution 4: Use Microsoft Store Python
1. Open Microsoft Store
2. Search for "Python 3.11" or "Python 3.12"
3. Install it
4. Try: `python -m pip install pdfplumber`

## 🧪 Test Your Setup

Run your `hello.py` file - it will now check what's installed:

```powershell
python hello.py
```

The script will tell you:
- ✅ Which libraries are installed
- ❌ Which libraries are missing
- 📋 What to do next

## 🎯 For PDF Practice Right Now

Even without pip working, you can:

1. **Practice with text files** (already working in your script)
2. **Learn the PDF reading concepts** (code is ready)
3. **Understand error handling** (built into the functions)

## 📚 Alternative: Use Online Python

If local setup is problematic:
- **Google Colab**: colab.research.google.com
- **Replit**: replit.com
- **CodePen**: codepen.io

These have pip pre-installed!

## 🎉 Once pip Works

```bash
pip install pdfplumber
pip install reportlab
```

Then uncomment the test code in `hello.py` and you're ready to go!

## 🆘 Still Having Issues?

Your `hello.py` script now includes:
- Library availability checker
- Detailed installation instructions
- Fallback text processing practice

Run it to see exactly what's available on your system!
