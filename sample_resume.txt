JOHN DOE
Software Developer

CONTACT INFORMATION
Email: <EMAIL>
Phone: (*************
Location: New York, NY

SUMMARY
Experienced software developer with 5+ years of expertise in Python, 
machine learning, and web development. Passionate about creating 
efficient solutions and working with cutting-edge technologies.

SKILLS
• Python Programming
• Machine Learning (scikit-learn, TensorFlow)
• Web Development (Django, Flask)
• Data Analysis (pandas, numpy)
• Database Management (SQL, MongoDB)
• Version Control (Git, GitHub)

EXPERIENCE

Senior Python Developer | Tech Solutions Inc. | 2021-Present
• Developed and maintained Python applications serving 10,000+ users
• Implemented machine learning models for data prediction
• Collaborated with cross-functional teams to deliver projects on time
• Mentored junior developers and conducted code reviews

Python Developer | StartupXYZ | 2019-2021
• Built web applications using Django framework
• Optimized database queries improving performance by 40%
• Integrated third-party APIs and payment systems
• Participated in agile development processes

EDUCATION
Bachelor of Science in Computer Science
University of Technology | 2015-2019

PROJECTS
• Resume Parser: AI-powered tool to extract information from resumes
• E-commerce Platform: Full-stack web application with payment integration
• Data Visualization Dashboard: Interactive charts for business analytics

CERTIFICATIONS
• Python Institute Certified Associate Programmer
• AWS Certified Developer Associate
• Machine Learning Specialization (Coursera)
